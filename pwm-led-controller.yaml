substitutions:
    name: 'pwm-led-controller'
    friendly_name: 'PWM LED Controller'

packages:
    - !include 'presets/esp32.yaml'

binary_sensor:
    - platform: gpio
      id: power_button
      name: 'Power Button'
      icon: 'mdi:power'
      pin:
          number: GPIO7
          mode: INPUT_PULLUP
          inverted: true
    - platform: gpio
      id: door_sensor
      name: 'Door Sensor'
      icon: 'mdi:door'
      pin:
          number: GPIO10
          mode: INPUT_PULLUP
          inverted: true

output:
    - platform: ledc
      id: led_output
      pin: GPIO5
      zero_means_zero: true

light:
    - platform: monochromatic
      output: led_output
      id: led
      name: 'LED'
      icon: 'mdi:led-outline'
      restore_mode: RESTORE_DEFAULT_OFF
