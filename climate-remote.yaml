substitutions:
    name: 'climate-ir-remote'
    friendly_name: 'Climate IR Remote'
    logger_level: 'DEBUG'

packages: !include 'presets/esp32.yaml'

remote_transmitter:
    id: ir_transmitter
    pin: GPIO7
    carrier_duty_percent: 33%

remote_receiver:
    id: ir_receiver
    pin:
        number: GPIO5
        inverted: true
        mode: INPUT_PULLUP
    tolerance: 60%
    buffer_size: 4kb
    dump: all
    rmt_channel: 2

climate:
    - platform: climate_ir_lg
      name: 'AC'
      supports_cool: true
      supports_heat: false
      receiver_id: ir_receiver
      transmitter_id: ir_transmitter
      header_high: 3400us
      header_low: 10000us
      bit_high: 460us
      bit_one_low: 1650us
      bit_zero_low: 560us
      visual:
          min_temperature: 16
          max_temperature: 30
          temperature_step: 1
