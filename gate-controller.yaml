substitutions:
    name: 'gate-controller'
    friendly_name: 'Gate Controller'
    logger_level: 'DEBUG'
    esp_board: 'esp32-c3-devkitm-1'

packages:
    - !include 'presets/esp32.yaml'

number:
    - platform: template
      id: 'switch_duration'
      name: 'Switch Duration'
      icon: 'mdi:timer-outline'
      entity_category: config
      unit_of_measurement: ms
      mode: box
      device_class: duration
      min_value: 100
      max_value: 1000
      step: 1
      optimistic: true
      restore_value: true
      initial_value: 200

binary_sensor:
    - platform: gpio
      id: state
      name: 'State'
      device_class: door
      icon: 'mdi:gate'
      pin:
          number: GPIO5
          mode: INPUT_PULLUP

    - platform: gpio
      id: emergency_button
      name: 'Emergency Button'
      icon: 'mdi:alert-circle'
      internal: true
      disabled_by_default: true
      pin:
          number: GPIO7
          mode: INPUT_PULLUP
          inverted: true
      on_multi_click:
          - timing:
                - ON for at most 1s
                - OFF for at most 1s
                - ON for at most 1s
                - OFF for at least 0.2s
            then:
                - logger.log: 'Emergency Button Double Clicked'
                - if:
                      condition:
                          binary_sensor.is_on: state
                      then:
                          - switch.turn_on: close_switch
                      else:
                          - switch.turn_on: open_switch

          - timing:
                - ON for at least 10s
            then:
                - logger.log: 'Emergency Button Long Clicked'
                - button.press: reboot_button

          - timing:
                - ON for at most 1s
                - OFF for at least 0.5s
            then:
                - logger.log: 'Emergency Button Single Clicked'
                - switch.turn_on: stop_switch

switch:
    - platform: gpio
      id: open_switch
      name: 'Open'
      internal: true
      disabled_by_default: true
      pin:
          number: GPIO10
      interlock: &interlock_group [open_switch, stop_switch, close_switch]
      on_turn_on:
          then:
              - switch.turn_off: stop_switch
              - switch.turn_off: close_switch
              - delay: !lambda 'return id(switch_duration).state;'
              - switch.turn_off: open_switch

    - platform: gpio
      id: stop_switch
      name: 'Stop'
      internal: true
      disabled_by_default: true
      pin:
          number: GPIO21
      interlock: *interlock_group
      on_turn_on:
          then:
              - switch.turn_off: open_switch
              - switch.turn_off: close_switch
              - delay: !lambda 'return id(switch_duration).state;'
              - switch.turn_off: stop_switch

    - platform: gpio
      id: close_switch
      name: 'Close'
      internal: true
      disabled_by_default: true
      pin:
          number: GPIO20
      interlock: *interlock_group
      on_turn_on:
          then:
              - switch.turn_off: open_switch
              - switch.turn_off: stop_switch
              - delay: !lambda 'return id(switch_duration).state;'
              - switch.turn_off: close_switch

cover:
    - platform: template
      id: gate
      name: 'Gate'
      device_class: gate
      icon: 'mdi:gate'
      optimistic: true
      assumed_state: true
      lambda: |-
          if (id(state).state) {
              return COVER_OPEN;
          } else {
              return COVER_CLOSED;
          }
      open_action:
          - switch.turn_on: open_switch
      close_action:
          - switch.turn_on: close_switch
      stop_action:
          - switch.turn_on: stop_switch
