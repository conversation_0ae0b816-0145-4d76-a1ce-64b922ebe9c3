substitutions:
    name: 'example'
    friendly_name: 'Example Config'
    logger_level: 'DEBUG'
    esp_board: esp32dev

packages: !include 'presets/esp32.yaml'

remote_transmitter:
    pin: GPIO16
    carrier_duty_percent: 100%

switch:
    - platform: template
      name: 'Ổ cắm RF'
      turn_on_action:
          - remote_transmitter.transmit_rc_switch_raw:
                code: '001010011011' # mã on thu được từ log
                protocol: 1
                repeat:
                    times: 6
                    wait_time: 10ms
      turn_off_action:
          - remote_transmitter.transmit_rc_switch_raw:
                code: '001010011000' # mã off
                protocol: 1
                repeat:
                    times: 6
                    wait_time: 10ms
