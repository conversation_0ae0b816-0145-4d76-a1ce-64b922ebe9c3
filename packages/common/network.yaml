substitutions:
    wifi_ssid: !secret wifi_ssid
    wifi_password: !secret wifi_password
    wifi_reboot_timeout: '10min'
    wifi_power_save: 'none'
    wifi_fast_connect: 'false'

wifi:
    ssid: $wifi_ssid
    password: $wifi_password
    ap:
    reboot_timeout: $wifi_reboot_timeout
    power_save_mode: $wifi_power_save
    fast_connect: $wifi_fast_connect

captive_portal:

improv_serial:

sensor:
    - platform: wifi_signal
      name: 'RSSI'
      entity_category: diagnostic
      icon: 'mdi:wifi'
      update_interval: $debug_update_interval

text_sensor:
    - platform: wifi_info
      ip_address:
          name: 'ESP IP Address'
          entity_category: diagnostic
          icon: 'mdi:ip-network'
      mac_address:
          name: 'ESP MAC Address'
          entity_category: diagnostic
          icon: 'mdi:network'
