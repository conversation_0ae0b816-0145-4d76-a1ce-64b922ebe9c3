substitutions:
    name: 'esphome-device'
    friendly_name: 'ESPHome Device'
    logger_level: 'DEBUG'
    debug_update_interval: '10s'

esphome:
    name: $name
    friendly_name: $friendly_name
    name_add_mac_suffix: true

logger:
    level: $logger_level

debug:
    update_interval: $debug_update_interval

api:

ota:
    - platform: esphome

text_sensor:
    - platform: uptime
      name: 'ESP Uptime'
      entity_category: diagnostic
      icon: 'mdi:timer-outline'
      update_interval: 60s
      format:
          separator: ' '

    - platform: debug
      reset_reason:
          name: 'ESP Reset Reason'
          entity_category: diagnostic
          icon: 'mdi:restart-alert'

button:
    - platform: restart
      id: reboot_button
      name: 'ESP Reboot'
      entity_category: diagnostic
      icon: 'mdi:restart'
      disabled_by_default: true

    - platform: factory_reset
      id: factory_reset_button
      name: 'ESP Factory Reset'
      entity_category: diagnostic
      icon: 'mdi:factory'
      disabled_by_default: true
