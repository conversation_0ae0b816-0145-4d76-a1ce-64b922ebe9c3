substitutions:
    scd40_update_interval: '10s'
    scd40_co2_name: 'SCD40 CO2'
    scd40_temperature_name: 'SCD40 Temperature'
    scd40_humidity_name: 'SCD40 Humidity'
    scd40_pressure_source: 'bmp180_pressure'

api:
    actions:
        - action: calibrate_scd40_co2_value
          variables:
              co2_ppm: int
          then:
              - scd4x.perform_forced_calibration:
                    value: !lambda 'return co2_ppm;'
                    id: scd40_sensor

sensor:
    - platform: scd4x
      id: scd40_sensor
      update_interval: $scd40_update_interval
      ambient_pressure_compensation_source: $scd40_pressure_source
      co2:
          name: $scd40_co2_name
      temperature:
          id: scd40_temperature
          name: $scd40_temperature_name
      humidity:
          id: scd40_humidity
          name: $scd40_humidity_name

button:
    - platform: template
      name: 'SCD40 Factory Reset'
      entity_category: diagnostic
      icon: 'mdi:factory'
      disabled_by_default: true
      on_press:
          then:
              - scd4x.factory_reset: scd40_sensor
