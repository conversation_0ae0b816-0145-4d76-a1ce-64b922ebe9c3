substitutions:
    sgp40_update_interval: '10s'
    sgp40_voc_name: 'SGP40 VOC'
    sgp40_temperature_source: 'bmp180_temperature'
    sgp40_humidity_source: 'scd40_humidity'

sensor:
    - platform: sgp4x
      update_interval: $sgp40_update_interval
      voc:
          name: $sgp40_voc_name
      compensation:
          temperature_source: $sgp40_temperature_source
          humidity_source: $sgp40_humidity_source
