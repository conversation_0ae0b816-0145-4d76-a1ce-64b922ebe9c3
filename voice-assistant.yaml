substitutions:
    name: 'voice-assistant'
    friendly_name: 'Voice Assistant'
    logger_level: 'DEBUG'
    esp_board: 'esp32dev'

packages:
    - !include 'presets/esp32.yaml'

i2c:
    sda: GPIO19
    scl: GPIO18
    scan: true

i2s_audio:
    - id: i2s_mic
      i2s_lrclk_pin: GPIO23
      i2s_bclk_pin: GPIO22

    - id: i2s_speaker
      i2s_lrclk_pin: GPIO13
      i2s_bclk_pin: GPIO12

display:
    - platform: ssd1306_i2c
      model: 'SSD1306 128x32'
      id: external_display
      lambda: |-
          it.line(0, 0, 50, 50);

microphone:
    - platform: i2s_audio
      id: external_mic
      adc_type: external
      i2s_din_pin: GPIO21
      i2s_audio_id: i2s_mic
      channel: left

media_player:
    - platform: i2s_audio
      id: external_media_player
      name: 'Media Player'
      dac_type: external
      i2s_dout_pin: GPIO14
      i2s_audio_id: i2s_speaker
      mode: mono

button:
    - platform: template
      name: 'Max Volume'
      on_press:
          then:
              - media_player.volume_set: 100%
