# ESPHome - Tài Liệu Tham Khảo Tổng Hợp

## Tổng Quan

**ESPHome** là một hệ thống cho phép bạn điều khiển các vi điều khiển ESP8266/ESP32 bằng các file cấu hình YAML đơn giản và tự động tạo ra mã C++ cho bạn.

### Phiên Bản Hiện Tại: 2025.6.0 (18/06/2025)

**Các tính năng mới quan trọng:**

-   **ESP-IDF 5.3.2**: Hỗ trợ ESP32-C6, ESP32-H2, ESP32-P4
-   **OpenThread**: Hỗ trợ mạng Thread cho ESP32-C6 và ESP32-H2
-   **Python 3.10+**: Yêu cầu tối thiểu
-   **Tối ưu hóa hiệu suất**: G<PERSON><PERSON>m RAM, tăng tốc API, gi<PERSON><PERSON> kích thước firmware

### Các ESP32 Được Hỗ Trợ

| Chip     | Cores  | Architecture | Wi-Fi | Bluetooth | Thread/Zigbee | Ghi Chú                |
| -------- | ------ | ------------ | ----- | --------- | ------------- | ---------------------- |
| ESP32    | Dual   | Xtensa LX6   | ✓     | ✓         | ✗             | Chip gốc               |
| ESP32-C3 | Single | RISC-V       | ✓     | 5.0 LE    | ✗             |                        |
| ESP32-C6 | Single | RISC-V       | 6     | 5.0 LE    | ✓             | **Mới**                |
| ESP32-H2 | Single | RISC-V       | ✗     | 5.0 LE    | ✓             | **Mới**                |
| ESP32-S2 | Single | Xtensa LX7   | ✓     | ✗         | ✗             |                        |
| ESP32-S3 | Dual   | Xtensa LX7   | ✓     | 5.0 LE    | ✗             |                        |
| ESP32-P4 | Dual   | RISC-V       | ✗     | ✗         | ✗             | **Mới**, hiệu suất cao |

## Cấu Trúc YAML Cơ Bản

### File Cấu Hình Tối Thiểu

```yaml
esphome:
    name: my-device
    friendly_name: My Device

esp32:
    board: esp32dev
    framework:
        type: arduino

# Enable logging
logger:

# Enable Home Assistant API
api:
    encryption:
        key: 'your-encryption-key'

ota:
    - platform: esphome
      password: 'your-ota-password'

wifi:
    ssid: !secret wifi_ssid
    password: !secret wifi_password

    # Enable fallback hotspot
    ap:
        ssid: 'My-Device Fallback Hotspot'
        password: 'fallback-password'

captive_portal:
```

### Cấu Hình Mạng

#### WiFi Cơ Bản

```yaml
wifi:
    ssid: MyHomeNetwork
    password: VerySafePassword

    # IP tĩnh (tùy chọn)
    manual_ip:
        static_ip: *************
        gateway: ***********
        subnet: *************
```

#### WiFi Nhiều Mạng

```yaml
wifi:
    networks:
        - ssid: FirstNetwork
          password: Password1
        - ssid: SecondNetwork
          password: Password2
```

#### Ethernet (ESP32)

```yaml
ethernet:
    type: LAN8720
    mdc_pin: GPIO23
    mdio_pin: GPIO18
    clk_mode: GPIO17_OUT
    phy_addr: 0
```

#### OpenThread (ESP32-C6/H2)

```yaml
openthread:
    # Cấu hình Thread network
```

### Web Server

```yaml
web_server:
    port: 80
    auth:
        username: !secret web_server_username
        password: !secret web_server_password
```

## Components Chính

### 1. Sensor (Cảm Biến)

#### Cấu Hình Cơ Bản

```yaml
sensor:
    - platform: dht
      pin: D2
      temperature:
          name: 'Living Room Temperature'
          unit_of_measurement: '°C'
          device_class: 'temperature'
          state_class: 'measurement'
          accuracy_decimals: 1
      humidity:
          name: 'Living Room Humidity'
          unit_of_measurement: '%'
          device_class: 'humidity'
          state_class: 'measurement'
      update_interval: 60s
```

#### Filters (Bộ Lọc)

```yaml
sensor:
    - platform: adc
      pin: A0
      name: 'Raw ADC'
      filters:
          - sliding_window_moving_average:
                window_size: 15
                send_every: 15
          - multiply: 3.3
          - calibrate_linear:
                - 0.0 -> 0.0
                - 1.0 -> 100.0
```

### 2. Binary Sensor (Cảm Biến Nhị Phân)

```yaml
binary_sensor:
    - platform: gpio
      pin:
          number: GPIO0
          inverted: true
          mode:
              input: true
              pullup: true
      name: 'Living Room Window'
      device_class: window

      # Events
      on_press:
          then:
              - switch.turn_on: relay_1
      on_release:
          then:
              - switch.turn_off: relay_1
```

#### Filters

```yaml
binary_sensor:
    - platform: gpio
      pin: D2
      name: 'Motion Sensor'
      filters:
          - invert:
          - delayed_on: 100ms
          - delayed_off: 100ms
          - autorepeat:
                - delay: 1s
                  time_off: 100ms
                  time_on: 900ms
```

### 3. Switch (Công Tắc)

#### GPIO Switch

```yaml
switch:
    - platform: gpio
      pin: GPIO2
      name: 'Living Room Dehumidifier'
      restore_mode: RESTORE_DEFAULT_OFF

      on_turn_on:
          then:
              - logger.log: 'Switch turned on!'
      on_turn_off:
          then:
              - logger.log: 'Switch turned off!'
```

#### Template Switch

```yaml
switch:
    - platform: template
      name: 'Template Switch'
      optimistic: true
      turn_on_action:
          - logger.log: 'Template switch turned on'
      turn_off_action:
          - logger.log: 'Template switch turned off'
```

### 4. Light (Đèn)

#### Monochromatic Light

```yaml
output:
    - platform: ledc
      pin: GPIO2
      id: gpio_2

light:
    - platform: monochromatic
      output: gpio_2
      name: 'Kitchen Light'
```

#### RGB Light

```yaml
output:
    - platform: ledc
      pin: GPIO5
      id: output_red
    - platform: ledc
      pin: GPIO4
      id: output_green
    - platform: ledc
      pin: GPIO15
      id: output_blue

light:
    - platform: rgb
      name: 'RGB Light'
      red: output_red
      green: output_green
      blue: output_blue
```

#### Light Effects

```yaml
light:
    - platform: rgb
      name: 'RGB Light'
      red: output_red
      green: output_green
      blue: output_blue
      effects:
          - random:
          - strobe:
          - flicker:
                alpha: 95%
                intensity: 1.5%
```

### 5. Climate (Điều Hòa)

#### Thermostat

```yaml
climate:
    - platform: thermostat
      name: 'Thermostat'
      sensor: temperature_sensor
      min_cooling_off_time: 300s
      min_cooling_run_time: 300s
      min_heating_off_time: 300s
      min_heating_run_time: 300s
      min_idle_time: 30s

      cool_action:
          - switch.turn_on: air_cond
      heat_action:
          - switch.turn_on: heater
      idle_action:
          - switch.turn_off: air_cond
          - switch.turn_off: heater

      default_preset: Home
      preset:
          - name: Home
            default_target_temperature_low: 20°C
            default_target_temperature_high: 22°C
```

### 6. Fan (Quạt)

#### Binary Fan

```yaml
output:
    - platform: gpio
      pin: GPIO2
      id: fan_output

fan:
    - platform: binary
      output: fan_output
      name: 'Living Room Fan'
```

#### Speed Fan

```yaml
output:
    - platform: ledc
      pin: GPIO2
      id: fan_speed

fan:
    - platform: speed
      output: fan_speed
      name: 'Living Room Fan'
```

### 7. Cover (Rèm/Cửa)

```yaml
cover:
    - platform: template
      name: 'Garage Door'
      device_class: garage

      open_action:
          - switch.turn_on: open_switch
          - delay: 0.1s
          - switch.turn_off: open_switch
      close_action:
          - switch.turn_on: close_switch
          - delay: 0.1s
          - switch.turn_off: close_switch
      stop_action:
          - switch.turn_off: open_switch
          - switch.turn_off: close_switch

      optimistic: true
      assumed_state: true
```

## I/O và Giao Tiếp

### I2C

```yaml
i2c:
    sda: GPIO21
    scl: GPIO22
    scan: true
    frequency: 400kHz
```

### SPI

```yaml
spi:
    clk_pin: GPIO18
    mosi_pin: GPIO23
    miso_pin: GPIO19
```

### UART

```yaml
uart:
    tx_pin: GPIO1
    rx_pin: GPIO3
    baud_rate: 9600
```

### Modbus

```yaml
modbus:
    send_wait_time: 200ms
    id: modbus1

modbus_controller:
    - id: device1
      address: 0x1
      modbus_id: modbus1
      setup_priority: -10
      update_interval: 10s
```

## Automations và Actions

### Triggers

```yaml
binary_sensor:
    - platform: gpio
      pin: D2
      name: 'Motion Sensor'
      on_press:
          then:
              - light.turn_on: living_room_light
              - delay: 5min
              - light.turn_off: living_room_light
```

### Conditions

```yaml
on_press:
    then:
        - if:
              condition:
                  binary_sensor.is_on: motion_sensor
              then:
                  - light.turn_on: light1
              else:
                  - light.turn_off: light1
```

### Lambda Functions

```yaml
sensor:
    - platform: template
      name: 'Calculated Value'
      lambda: |-
          return id(sensor1).state * 2.0 + id(sensor2).state;
      update_interval: 60s
```

## Packages và External Components

### Packages

```yaml
packages:
    wifi: !include common/wifi.yaml
    device_base: !include common/device_base.yaml
```

### External Components

```yaml
external_components:
    - source: github://esphome/esphome@dev
      components: [rtttl, dfplayer]
    - source:
          type: local
          path: my_components
```

## Secrets và Substitutions

### secrets.yaml

```yaml
wifi_ssid: 'MyWiFiNetwork'
wifi_password: 'MyWiFiPassword'
api_encryption_key: 'your-32-char-encryption-key'
```

### Substitutions

```yaml
substitutions:
    device_name: living-room-sensor
    friendly_name: 'Living Room Sensor'

esphome:
    name: ${device_name}
    friendly_name: ${friendly_name}
```

## Breaking Changes trong 2025.6.0

### 1. Python 3.10+ Required

-   **Ảnh hưởng**: Cần Python 3.10 trở lên
-   **Hành động**: Nâng cấp Python trước khi cập nhật ESPHome

### 2. ESP-IDF 4.x Deprecated

-   **Ảnh hưởng**: Phiên bản 2025.7.0 sẽ không hỗ trợ ESP-IDF 4.x
-   **Hành động**: Chuyển sang ESP-IDF 5.x

### 3. Memory Layout Changes

-   **Ảnh hưởng**: Thay đổi cách lưu trữ state của component
-   **Hành động**: Có thể cần compile lại firmware

## Troubleshooting và Best Practices

### Common Issues

#### 1. WiFi Connection Problems

```yaml
wifi:
    ssid: !secret wifi_ssid
    password: !secret wifi_password

    # Thêm cấu hình power save
    power_save_mode: none

    # Cấu hình manual IP nếu DHCP có vấn đề
    manual_ip:
        static_ip: *************
        gateway: ***********
        subnet: *************
        dns1: *******
        dns2: *******
```

#### 2. OTA Update Failures

```yaml
ota:
    - platform: esphome
      password: !secret ota_password
      # Tăng timeout cho mạng chậm
      num_attempts: 3
      reboot_timeout: 5min
```

#### 3. Memory Issues

```yaml
# Giảm log level để tiết kiệm RAM
logger:
    level: WARN

# Tắt web server nếu không cần
# web_server:

# Sử dụng PSRAM nếu có
psram:
    mode: octal
    speed: 80MHz
```

### Performance Optimization

#### 1. Update Intervals

```yaml
sensor:
    - platform: dht
      pin: D2
      # Tăng update_interval để giảm tải CPU
      update_interval: 60s
      temperature:
          name: 'Temperature'
          # Sử dụng filters để giảm noise
          filters:
              - sliding_window_moving_average:
                    window_size: 5
                    send_every: 5
```

#### 2. Component Priority

```yaml
# Đặt priority cho các component quan trọng
sensor:
    - platform: dht
      pin: D2
      setup_priority: 800 # Cao hơn default (600)
```

#### 3. Loop Time Optimization

```yaml
# Giảm tần suất loop cho component không quan trọng
binary_sensor:
    - platform: gpio
      pin: D1
      name: 'Button'
      # Sử dụng interrupt thay vì polling
      on_press:
          then:
              - logger.log: 'Button pressed'
```

## Advanced Features

### 1. Custom Components

```yaml
# my_custom_component.h
esphome:
    includes:
        - my_custom_component.h

custom_component:
    - lambda: |-
          auto my_custom = new MyCustomComponent();
          return {my_custom};
```

### 2. Deep Sleep

```yaml
deep_sleep:
    run_duration: 30s
    sleep_duration: 5min
    id: deep_sleep_1

# Wake up từ GPIO
binary_sensor:
    - platform: gpio
      pin:
          number: GPIO0
          mode:
              input: true
              pullup: true
      name: 'Wake Up Button'
      on_press:
          then:
              - deep_sleep.prevent: deep_sleep_1
```

### 3. Time Component

```yaml
time:
    - platform: sntp
      id: sntp_time
      timezone: Asia/Ho_Chi_Minh
      servers:
          - 0.pool.ntp.org
          - 1.pool.ntp.org

# Sử dụng time trong automation
automation:
    - platform: time
      at: '07:00:00'
      then:
          - switch.turn_on: morning_light
```

### 4. Interval Component

```yaml
interval:
    - interval: 30s
      then:
          - if:
                condition:
                    wifi.connected:
                then:
                    - logger.log: 'WiFi is connected'
                else:
                    - logger.log: 'WiFi disconnected'
```

## Device Classes và Icons

### Sensor Device Classes

```yaml
sensor:
    - platform: dht
      pin: D2
      temperature:
          name: 'Temperature'
          device_class: temperature
          state_class: measurement
          unit_of_measurement: '°C'
      humidity:
          name: 'Humidity'
          device_class: humidity
          state_class: measurement
          unit_of_measurement: '%'
```

### Binary Sensor Device Classes

```yaml
binary_sensor:
    - platform: gpio
      pin: D1
      name: 'Door Sensor'
      device_class: door # door, window, motion, etc.

    - platform: gpio
      pin: D2
      name: 'Motion Sensor'
      device_class: motion
```

### Custom Icons

```yaml
sensor:
    - platform: template
      name: 'Custom Sensor'
      icon: 'mdi:thermometer'
      lambda: |-
          return 25.0;
```

## API Reference

### Home Assistant API

```yaml
api:
    encryption:
        key: !secret api_encryption_key

    # Custom services
    services:
        - service: control_led
          variables:
              brightness: int
              color: string
          then:
              - light.turn_on:
                    id: led_strip
                    brightness: !lambda 'return brightness / 100.0;'
                    red: !lambda |-
                        if (color == "red") return 1.0;
                        return 0.0;
```

### MQTT Integration

```yaml
mqtt:
    broker: *************
    username: !secret mqtt_username
    password: !secret mqtt_password
    discovery: true
    discovery_prefix: homeassistant

    # Custom topics
    on_message:
        - topic: esphome/my_device/command
          then:
              - logger.log:
                    format: 'Received command: %s'
                    args: ['x.c_str()']
```

## Component List (Alphabetical)

### A-C

-   **ADC**: Analog to Digital Converter
-   **ADS1115**: 16-bit ADC
-   **AHT10/AHT20**: Temperature & Humidity
-   **AM2320**: Temperature & Humidity
-   **API**: Home Assistant Native API
-   **Binary Sensor**: On/Off sensors
-   **BLE**: Bluetooth Low Energy
-   **BME280/BME680**: Environmental sensors
-   **Button**: Momentary switches
-   **Climate**: HVAC control
-   **Cover**: Blinds, garage doors

### D-L

-   **Dallas**: DS18B20 temperature sensors
-   **DHT**: DHT11/DHT22 sensors
-   **Display**: LCD, OLED, E-Paper
-   **ESP32**: ESP32 platform configuration
-   **Ethernet**: Wired network
-   **Fan**: Fan control
-   **GPIO**: General Purpose I/O
-   **I2C**: Inter-Integrated Circuit
-   **Light**: LED control
-   **Logger**: Debug logging

### M-S

-   **MQTT**: Message broker
-   **Number**: Numeric input
-   **OTA**: Over-The-Air updates
-   **Output**: PWM, GPIO outputs
-   **Packages**: Configuration reuse
-   **Sensor**: Analog sensors
-   **SPI**: Serial Peripheral Interface
-   **Switch**: On/Off controls

### T-Z

-   **Text Sensor**: String values
-   **Time**: Time synchronization
-   **UART**: Serial communication
-   **Update**: Firmware updates
-   **WiFi**: Wireless network
-   **Web Server**: HTTP interface

---

_Tài liệu này được tạo dựa trên ESPHome 2025.6.0 - Cập nhật: 22/06/2025_
