substitutions:
    name: 'watering'
    friendly_name: 'Watering'
    logger_level: 'DEBUG'

packages: !include 'presets/esp32.yaml'

output:
    - platform: gpio
      id: soil_moisture_output
      pin:
          number: GPIO4

switch:
    - platform: output
      id: soil_moisture_switch
      name: 'Soil Moisture Switch'
      output: soil_moisture_output
      internal: true
      disabled_by_default: true

    - platform: gpio
      id: pump
      name: 'Pump'
      icon: 'mdi:water-pump'
      pin:
          number: GPIO5

sensor:
    - platform: 'adc'
      id: soil_moisture
      name: 'Soil Moisture'
      icon: 'mdi:water-percent'
      pin:
          number: GPIO3
      attenuation: 12db
      update_interval: never
      unit_of_measurement: '%'
      filters:
          - calibrate_linear:
                - 2.91300 -> 0.0
                - 0.18800 -> 100.0
          - lambda: |-
                if (x < 0) {
                    return 0;
                } else if (x > 100) {
                    return 100;
                } else {
                    return x;
                }

interval:
    - interval: 10s
      then:
          - switch.turn_on: soil_moisture_switch
          - delay: 500ms
          - component.update: soil_moisture
          - delay: 100ms
          - switch.turn_off: soil_moisture_switch
